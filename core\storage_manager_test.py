#!/usr/bin/env python3
"""
Clean Storage Manager Module

This module provides streamlined functionality for storing backup data in Alibaba Cloud OSS.
It uses dedicated service classes for compression and checksum operations,
following the Single Responsibility Principle.

REFACTORED: Reduced from 1700+ lines to ~200 lines by using:
- CompressionService: Handles all compression operations (GZ-optimized)
- ChecksumService: Handles integrity verification
- Unified error handling using custom exceptions
"""

import os
import datetime
from typing import List, Tuple, Optional, Dict, Any

# Import OSS SDK
try:
    import oss2
except ImportError:
    raise ImportError("Alibaba Cloud OSS SDK not found. Please install it with 'pip install oss2'")

# Import project modules
from core.config_manager import ConfigManager
from core.compression_service import CompressionService
from core.checksum_service import ChecksumService
from utils.exceptions import StorageError, NetworkError, ValidationError
from utils.minimal_logging import logger

class StorageManager:
    """
    Clean storage manager that uses dedicated service classes for all operations.
    
    This class focuses solely on OSS operations and delegates compression and
    checksum operations to specialized service classes.
    """

    def __init__(self, config_manager: ConfigManager = None):
        """
        Initialize the storage manager with service dependencies.

        Args:
            config_manager: Configuration manager instance (optional)
        """
        self.config = config_manager or ConfigManager()
        self._auth = None
        self._bucket = None

        # Initialize service dependencies
        self.compression_service = CompressionService(self.config)
        self.checksum_service = ChecksumService(self.config)
        logger.info("StorageManager initialized")

        # Get OSS credentials
        self.credentials = self.config.get_oss_credentials()

    def get_oss_auth(self):
        """Get OSS authentication object."""
        if self._auth is None:
            self._auth = oss2.Auth(
                self.credentials['access_key_id'],
                self.credentials['access_key_secret']
            )
        return self._auth

    def get_oss_bucket(self):
        """Get OSS bucket object."""
        if self._bucket is None:
            auth = self.get_oss_auth()
            self._bucket = oss2.Bucket(
                auth,
                self.credentials['endpoint'],
                self.credentials['bucket']
            )
        return self._bucket

    def compress_and_upload(self, local_dir: str, oss_path: str, 
                           verify_integrity: bool = True) -> Tuple[bool, Dict[str, Any]]:
        """
        Compress a directory and upload to OSS with optional integrity verification.

        Args:
            local_dir: Local directory to compress and upload
            oss_path: OSS path for the uploaded file
            verify_integrity: Whether to verify file integrity

        Returns:
            Tuple of (success_flag, operation_details)
        """
        try:
            logger.log_operation("Compress and Upload", "STARTED", f"{local_dir} -> {oss_path}")

            # Create temporary file for compression
            temp_file = f"{local_dir}.tar.gz"
            
            # Step 1: Compress using CompressionService (GZ format)
            success, compressed_file, compression_stats = self.compression_service.compress_directory(
                local_dir, temp_file
            )
            
            if not success:
                raise StorageError("Compression failed", operation="compress", file_path=local_dir)

            # Step 2: Calculate checksum if verification is enabled
            checksum = None
            if verify_integrity:
                checksum = self.checksum_service.calculate_file_checksum(compressed_file)
                if not checksum:
                    raise ValidationError("Checksum calculation failed", file_path=compressed_file)

            # Step 3: Upload to OSS
            upload_success, upload_details = self._upload_file_to_oss(compressed_file, oss_path)
            
            if not upload_success:
                raise StorageError("Upload to OSS failed", operation="upload", file_path=oss_path)

            # Step 4: Verify upload integrity if enabled
            if verify_integrity and checksum:
                verification_success = self._verify_uploaded_file(oss_path, checksum)
                if not verification_success:
                    raise ValidationError("Upload integrity verification failed", file_path=oss_path)

            # Step 5: Cleanup temporary file
            if os.path.exists(compressed_file):
                os.remove(compressed_file)

            # Prepare operation details
            operation_details = {
                'local_dir': local_dir,
                'oss_path': oss_path,
                'compressed_file': compressed_file,
                'compression_stats': compression_stats,
                'upload_details': upload_details,
                'checksum': checksum,
                'verified': verify_integrity
            }

            logger.log_operation("Compress and Upload", "SUCCESS", oss_path)
            return True, operation_details

        except Exception as e:
            logger.log_operation("Compress and Upload", "FAILED", str(e))
            # Cleanup on error
            if 'compressed_file' in locals() and os.path.exists(compressed_file):
                os.remove(compressed_file)
            return False, {'error': str(e)}

    def _upload_file_to_oss(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload a file to OSS.

        Args:
            local_file: Local file path
            oss_path: OSS destination path

        Returns:
            Tuple of (success_flag, upload_details)
        """
        try:
            bucket = self.get_oss_bucket()
            
            # Get file size for progress tracking
            file_size = os.path.getsize(local_file)

            # Upload file
            start_time = datetime.datetime.now()
            result = bucket.put_object_from_file(oss_path, local_file)
            end_time = datetime.datetime.now()

            upload_time = (end_time - start_time).total_seconds()
            upload_speed = file_size / upload_time if upload_time > 0 else 0

            upload_details = {
                'file_size': file_size,
                'upload_time': upload_time,
                'upload_speed_mbps': upload_speed / (1024 * 1024),
                'etag': result.etag,
                'request_id': result.request_id
            }

            size_mb = file_size / (1024 * 1024)
            speed_mbps = upload_speed / (1024 * 1024)
            logger.info(f"Upload completed: {size_mb:.1f}MB in {upload_time:.1f}s ({speed_mbps:.1f}MB/s)")
            return True, upload_details

        except Exception as e:
            logger.error(f"Upload failed: {str(e)}")
            return False, {'error': str(e)}

    def _verify_uploaded_file(self, oss_path: str, expected_checksum: str) -> bool:
        """
        Verify the integrity of an uploaded file by comparing checksums.

        Args:
            oss_path: OSS path of the uploaded file
            expected_checksum: Expected checksum of the file

        Returns:
            True if verification passes, False otherwise
        """
        try:
            bucket = self.get_oss_bucket()
            
            # Download file to temporary location for verification
            temp_file = f"temp_verify_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.tmp"
            
            bucket.get_object_to_file(oss_path, temp_file)
            
            # Calculate checksum of downloaded file
            actual_checksum = self.checksum_service.calculate_file_checksum(temp_file)
            
            # Compare checksums
            verification_passed = actual_checksum == expected_checksum
            
            # Cleanup temporary file
            if os.path.exists(temp_file):
                os.remove(temp_file)

            if verification_passed:
                logger.info("Integrity verification: PASSED")
            else:
                logger.error("Integrity verification: FAILED")

            return verification_passed

        except Exception as e:
            logger.error(f"Verification error: {str(e)}")
            return False

    def list_oss_objects(self, prefix: str = "") -> List[str]:
        """
        List objects in OSS bucket with optional prefix filter.

        Args:
            prefix: Prefix to filter objects

        Returns:
            List of object keys
        """
        try:
            bucket = self.get_oss_bucket()
            objects = []
            
            for obj in oss2.ObjectIterator(bucket, prefix=prefix):
                objects.append(obj.key)
                
            logger.info(f"Listed {len(objects)} OSS objects")
            return objects

        except Exception as e:
            logger.error(f"List objects failed: {str(e)}")
            return []

    def delete_oss_object(self, oss_path: str) -> bool:
        """
        Delete an object from OSS.

        Args:
            oss_path: OSS path of the object to delete

        Returns:
            True if deletion successful, False otherwise
        """
        try:
            bucket = self.get_oss_bucket()
            bucket.delete_object(oss_path)
            logger.info(f"Deleted: {oss_path}")
            return True

        except Exception as e:
            logger.error(f"Delete failed: {str(e)}")
            return False

    def get_object_info(self, oss_path: str) -> Optional[Dict[str, Any]]:
        """
        Get information about an OSS object.

        Args:
            oss_path: OSS path of the object

        Returns:
            Dictionary with object information or None if not found
        """
        try:
            bucket = self.get_oss_bucket()
            result = bucket.head_object(oss_path)
            
            object_info = {
                'size': result.content_length,
                'last_modified': result.last_modified,
                'etag': result.etag,
                'content_type': result.content_type
            }
            
            return object_info

        except Exception as e:
            logger.error(f"Get object info failed: {str(e)}")
            return None
