import unittest
import os
from unittest.mock import Mock, patch, MagicMock
from core.unified_table_processor import (
    UnifiedTableProcessor,
    SmartProcessingStrategy,
    BasicProcessingStrategy
)
from core.backup_config import BackupConfig
from utils.exceptions import ValidationError



class TestSmartProcessingStrategy(unittest.TestCase):
    def setUp(self):
        self.config = BackupConfig()
        self.strategy = SmartProcessingStrategy()
        
        # Mock dependencies with more realistic return values
        self.mock_devo = MagicMock()
        self.mock_storage = MagicMock()
        self.mock_chunk = MagicMock()
        
        # Setup default mock behaviors
        self.mock_devo.execute_query.return_value = [{'total': 100}]
        self.mock_chunk.query_and_save_data.return_value = (100, 1)
        self.mock_storage.compress_and_upload.return_value = (
            True, {
                'upload_details': {'file_size': 1024},
                'checksum': 'abc123',
                'compression_stats': {'ratio': 0.5}
            }
        )

        
        # Configure mock returns
        self.mock_devo.execute_query.return_value = [{'total': 100}]
        self.mock_chunk.query_and_save_data.return_value = (100, 1)
        self.mock_storage.compress_and_upload.return_value = (
            True, {'upload_details': {}, 'checksum': 'abc123'}
        )

    def test_process_tables_success(self):
        """Test successful table processing with full verification"""
        result = self.strategy.process_tables(
            ['test.table1', 'test.table2'],
            self.config,
            devo_client=self.mock_devo,
            storage_manager=self.mock_storage,
            chunk_manager=self.mock_chunk
        )
        
        # Verify overall status
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['summary']['successful_tables'], 2)
        
        # Verify table results
        self.assertIn('test.table1', result['table_results'])
        self.assertEqual(result['table_results']['test.table1']['status'], 'success')
        self.assertEqual(result['table_results']['test.table1']['rows_backed_up'], 100)
        
        # Verify storage details
        self.assertTrue(result['table_results']['test.table1']['verified'])


    def test_empty_table_skipping(self):
        """Test empty table detection and skipping with various scenarios"""
        test_cases = [
            ([{'total': 0}], True, 'skipped'),
            ([{'total': 1}], True, 'success'),
            ([{'total': 0}], False, 'success'),
            (None, True, 'skipped')  # Simulate query failure
        ]
        
        for query_result, skip_empty, expected_status in test_cases:
            with self.subTest(query_result=query_result, skip_empty=skip_empty):
                self.mock_devo.execute_query.return_value = query_result
                result = self.strategy.process_tables(
                    ['test.table'],
                    self.config,
                    skip_empty=skip_empty,
                    devo_client=self.mock_devo,
                    storage_manager=self.mock_storage,
                    chunk_manager=self.mock_chunk
                )
                self.assertEqual(result['table_results']['test.table']['status'], expected_status)


    def test_error_handling_scenarios(self):
        """Test various error scenarios with proper error propagation"""
        error_cases = [
            ('compression', (False, {'error': 'Compression failed'}), 'failed'),
            ('upload', (False, {'error': 'Upload failed'}), 'failed'),
            ('query', Exception('Query failed'), 'error'),
            ('validation', ValidationError('Invalid data'), 'error')
        ]
        
        for error_type, mock_behavior, expected_status in error_cases:
            with self.subTest(error_type=error_type):
                if error_type == 'compression':
                    self.mock_storage.compress_and_upload.return_value = mock_behavior
                elif error_type == 'upload':
                    self.mock_storage.compress_and_upload.return_value = mock_behavior
                elif error_type == 'query':
                    self.mock_devo.execute_query.side_effect = mock_behavior
                elif error_type == 'validation':
                    self.mock_storage.compress_and_upload.side_effect = mock_behavior
                
                result = self.strategy.process_tables(
                    ['test.table'],
                    self.config,
                    devo_client=self.mock_devo,
                    storage_manager=self.mock_storage,
                    chunk_manager=self.mock_chunk
                )
                self.assertEqual(result['table_results']['test.table']['status'], expected_status)


class TestBasicProcessingStrategy(unittest.TestCase):
    def setUp(self):
        self.config = Mock()
        self.strategy = BasicProcessingStrategy()
        self.mock_devo = MagicMock()
        self.mock_storage = MagicMock()
        self.mock_chunk = MagicMock()

    def test_basic_processing_flow(self):
        """Test basic processing without skipping empty tables"""
        self.mock_devo.execute_query.return_value = [{'total': 0}]
        result = self.strategy.process_tables(
            ['empty.table'],
            self.config,
            devo_client=self.mock_devo,
            storage_manager=self.mock_storage,
            chunk_manager=self.mock_chunk
        )
        self.assertEqual(result['table_results']['empty.table']['status'], 'processed')

class TestUnifiedTableProcessor(unittest.TestCase):
    def setUp(self):
        self.config = Mock()
        self.processor = UnifiedTableProcessor(self.config)

    @patch('core.unified_table_processor.SmartProcessingStrategy')
    def test_strategy_selection(self, mock_strategy):
        """Test automatic strategy selection"""
        mock_strategy.return_value.process_tables.return_value = {'status': 'success'}
        result = self.processor.process_tables(['test.table'])
        self.assertEqual(result['status'], 'success')
        mock_strategy.return_value.process_tables.assert_called_once()

if __name__ == '__main__':
    unittest.main()
