@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Daily Backup System                               S4NG-7
REM ===============================================================
REM Unified daily backup system with integrated functionality:
REM - Daily backup operations
REM - Single table backup
REM - System testing and validation
REM - Task scheduler setup
REM - Comprehensive testing framework
REM
REM Features:
REM - Sequential table processing (one at a time)
REM - Comprehensive error handling with retry mechanism
REM - Email notifications with detailed summaries
REM - Automatic cleanup and validation
REM - Performance monitoring and reporting
REM
REM Usage:
REM   run_daily_backup.bat [mode] [options]
REM
REM Modes:
REM   backup             Run daily backup (default)
REM   single-table       Backup single table
REM   test               Run system tests
REM   setup              Setup task scheduler
REM   help               Show this help message
REM
REM Backup Options:
REM   --dry-run          Validate tables only, no backup
REM   --single-table     Process only one table for testing
REM   --force-email      Send email even in test mode
REM   --verbose          Enable verbose logging
REM   --chunk-size N     Override default chunk size
REM   --timeout N        Override default timeout
REM
REM Single Table Options:
REM   <table_name>       Table name to backup
REM   --days N           Number of days to backup (default: 1)
REM   --list-tables      List all available tables
REM
REM Test Options:
REM   dry-run            Validate tables only
REM   single-table       Test single table backup
REM   full-test          Test complete backup process
REM   performance        Run performance benchmarks
REM   error-handling     Test error scenarios
REM   all-tests          Run comprehensive test suite
REM   quick              Run quick test suite
REM
REM Setup Options:
REM   create-daily       Create daily backup task
REM   create-weekly      Create weekly backup task
REM   create-monthly     Create monthly backup task
REM   create-all         Create all backup tasks
REM   list               List all TNGD backup tasks
REM   status             Show status of backup tasks
REM   delete             Delete backup tasks
REM ===============================================================

REM Set the current directory to the script directory
cd /d "%~dp0"

REM Set default values
set DAILY_BACKUP_SCRIPT=scripts\daily_backup_scheduler.py
set TEST_SCRIPT=scripts\test_backup_system.py
set SCHEDULER_SCRIPT=scripts\setup_daily_schedule.py
set PYTHON_CMD=python
set CURRENT_DIR=%~dp0

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

REM Get current date and time for logging
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%"
set "Min=%dt:~10,2%"
set "Sec=%dt:~12,2%"
set "LOG_FILE=logs\daily\%YYYY%-%MM%-%DD%\daily_backup_%HH%%Min%%Sec%.log"

REM Create daily log directory
if not exist "logs\daily\%YYYY%-%MM%-%DD%" mkdir "logs\daily\%YYYY%-%MM%-%DD%"

REM Process command line arguments and determine mode
set MODE=backup
if "%1"=="" goto run_backup
if "%1"=="backup" (
    set MODE=backup
    shift
    goto run_backup
)
if "%1"=="single-table" (
    set MODE=single-table
    shift
    goto single_table_mode
)
if "%1"=="test" (
    set MODE=test
    shift
    goto test_mode
)
if "%1"=="setup" (
    set MODE=setup
    shift
    goto setup_mode
)
if "%1"=="help" goto help
if "%1"=="--help" goto help
if "%1"=="-h" goto help

REM If first argument starts with --, treat as backup mode with options
if "%1:~0,2%"=="--" goto run_backup

REM Default to backup mode
goto run_backup

:run_backup
echo ===============================================================
echo TNGD DAILY BACKUP SYSTEM
echo ===============================================================
echo Current time: %date% %time%
echo.

REM Check if tables.json exists
if not exist tabletest\tables.json (
    echo ERROR: tabletest\tables.json not found!
    echo The backup cannot proceed without the table list.
    echo Please make sure the file exists and contains the list of tables to back up.
    exit /b 1
)

REM Count tables in the JSON file
for /f "tokens=1 delims=:" %%a in ('findstr /n /c:"]" tabletest\tables.json') do set total_lines=%%a
for /f "tokens=1 delims=:" %%a in ('findstr /n /c:"[" tabletest\tables.json') do set start_line=%%a
set /a table_count=total_lines-start_line-1
echo Found approximately %table_count% tables in tabletest\tables.json

echo Configuration:
echo - Using sequential processing (one table at a time)
echo - Comprehensive error handling with exponential backoff retry
echo - Email notifications enabled
echo - Automatic cleanup and validation
echo - Performance monitoring enabled
echo - Logs will be stored in %LOG_FILE%
echo ===============================================================
echo.

REM Check disk space before starting backup
echo Checking disk space and performing cleanup...
%PYTHON_CMD% utils\disk_cleanup.py --force --verbose
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Disk space is low. Running emergency cleanup...
    %PYTHON_CMD% utils\disk_cleanup.py --emergency --verbose
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Not enough disk space available for backup.
        echo Please free up disk space and try again.
        exit /b 1
    )
)

REM Collect all parameters
set "EXTRA_PARAMS="
:collect_params
if "%~1"=="" goto end_collect_params
set "EXTRA_PARAMS=%EXTRA_PARAMS% %~1"
shift
goto collect_params
:end_collect_params

REM Run the daily backup scheduler
echo Starting daily backup process...
echo Logging to %LOG_FILE%
echo.
echo Running command: %PYTHON_CMD% %DAILY_BACKUP_SCRIPT% %EXTRA_PARAMS%
%PYTHON_CMD% %DAILY_BACKUP_SCRIPT% %EXTRA_PARAMS% > %LOG_FILE% 2>&1

set BACKUP_EXIT_CODE=%ERRORLEVEL%

REM Display results
echo.
echo ===============================================================
echo DAILY BACKUP COMPLETED at %date% %time%
echo Exit code: %BACKUP_EXIT_CODE%
echo ===============================================================

REM Interpret exit codes
if %BACKUP_EXIT_CODE% EQU 0 (
    echo ✅ Daily backup completed successfully!
    echo All tables were processed without errors.
) else if %BACKUP_EXIT_CODE% EQU 1 (
    echo ⚠️  Daily backup completed with some issues.
    echo Some tables may have failed. Check the logs for details.
) else (
    echo ❌ Daily backup failed.
    echo Please check the logs for error details.
)

echo.
echo Log file: %LOG_FILE%
echo.

REM Run post-backup cleanup
echo Performing post-backup cleanup...
%PYTHON_CMD% utils\disk_cleanup.py --force --verbose

REM Show recent log entries for quick review
echo ===============================================================
echo RECENT LOG ENTRIES (Last 20 lines):
echo ===============================================================
if exist "%LOG_FILE%" (
    powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last 20"
) else (
    echo Log file not found.
)
echo ===============================================================

exit /b %BACKUP_EXIT_CODE%

:single_table_mode
echo ===============================================================
echo TNGD SINGLE TABLE BACKUP UTILITY
echo ===============================================================
echo.

REM Check if any arguments provided
if "%1"=="" (
    echo Usage: run_daily_backup.bat single-table ^<table_name^> [options]
    echo.
    echo Examples:
    echo   run_daily_backup.bat single-table my.app.tngd.waf
    echo   run_daily_backup.bat single-table my.app.tngd.waf --days 7 --chunk-size 50000
    echo   run_daily_backup.bat single-table my.app.tngd.waf --dry-run
    echo   run_daily_backup.bat single-table --list-tables
    echo.
    echo Available options:
    echo   --list-tables     List all available tables
    echo   --dry-run         Test mode - validate only, no backup
    echo   --days N          Number of days to backup (default: 1)
    echo   --chunk-size N    Chunk size for processing (default: 100000)
    echo   --verbose         Enable detailed logging
    echo.
    pause
    exit /b 1
)

REM Handle list tables request
if /i "%1"=="--list-tables" (
    echo Listing available tables...
    echo.
    python backup_single_table.py --list-tables
    echo.
    pause
    exit /b 0
)

REM Display what we're doing
echo Table to backup: %1
if not "%2"=="" echo Additional options: %2 %3 %4 %5 %6 %7 %8 %9
echo.
echo ===============================================================
echo 🔄 STARTING BACKUP PROCESS
echo ===============================================================
echo.

REM Run the backup
echo Running command: python backup_single_table.py %*
python backup_single_table.py %*

REM Capture exit code
set BACKUP_EXIT_CODE=%ERRORLEVEL%

echo.
echo ===============================================================
echo 📊 BACKUP COMPLETED
echo ===============================================================
echo.

REM Display results based on exit code
if %BACKUP_EXIT_CODE% EQU 0 (
    echo ✅ SUCCESS: Table backup completed successfully!
    echo.
    echo 📋 What happened:
    echo   • Table data extracted and compressed
    echo   • Backup uploaded to OSS storage
    echo   • Temporary files cleaned up
    echo   • Process completed without errors
    echo.
    echo 📁 Check the logs directory for detailed information
) else (
    echo ❌ FAILURE: Table backup encountered errors
    echo.
    echo 📋 Troubleshooting:
    echo   • Check if the table name is correct
    echo   • Verify database connectivity
    echo   • Review log files for detailed error information
    echo   • Try running with --dry-run to test connectivity
    echo.
    echo 🔧 Common solutions:
    echo   • Use --list-tables to see available tables
    echo   • Check network connectivity to database
    echo   • Verify OSS credentials and permissions
)

echo.
pause
exit /b %BACKUP_EXIT_CODE%

:test_mode
echo ===============================================================
echo TNGD BACKUP SYSTEM TESTING FRAMEWORK
echo ===============================================================
echo.

REM Check test type
set TEST_TYPE=%1
if "%TEST_TYPE%"=="" set TEST_TYPE=help

if "%TEST_TYPE%"=="help" goto test_help
if "%TEST_TYPE%"=="dry-run" goto test_dry_run
if "%TEST_TYPE%"=="single-table" goto test_single_table
if "%TEST_TYPE%"=="full-test" goto test_full_test
if "%TEST_TYPE%"=="performance" goto test_performance
if "%TEST_TYPE%"=="error-handling" goto test_error_handling
if "%TEST_TYPE%"=="all-tests" goto test_all_tests
if "%TEST_TYPE%"=="quick" goto test_quick

echo Unknown test type: %TEST_TYPE%
goto test_help

:test_dry_run
echo Running dry-run validation test...
set LOG_FILE=logs\tests\test_run_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log
%PYTHON_CMD% %TEST_SCRIPT% --dry-run --verbose > %LOG_FILE% 2>&1
set TEST_EXIT_CODE=%ERRORLEVEL%
echo Test completed with exit code: %TEST_EXIT_CODE%
if %TEST_EXIT_CODE% EQU 0 (
    echo ✅ Dry-run validation test PASSED!
) else (
    echo ❌ Dry-run validation test FAILED!
)
echo Log file: %LOG_FILE%
pause
exit /b %TEST_EXIT_CODE%

:test_single_table
echo Running single table backup test...
set LOG_FILE=logs\tests\test_run_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log
%PYTHON_CMD% %TEST_SCRIPT% --single-table --verbose > %LOG_FILE% 2>&1
set TEST_EXIT_CODE=%ERRORLEVEL%
echo Test completed with exit code: %TEST_EXIT_CODE%
if %TEST_EXIT_CODE% EQU 0 (
    echo ✅ Single table backup test PASSED!
) else (
    echo ❌ Single table backup test FAILED!
)
echo Log file: %LOG_FILE%
pause
exit /b %TEST_EXIT_CODE%

:test_full_test
echo Running full system backup test...
set TABLE_LIMIT=5
if not "%2"=="" set TABLE_LIMIT=%2
set LOG_FILE=logs\tests\test_run_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log
%PYTHON_CMD% %TEST_SCRIPT% --full-test --table-limit %TABLE_LIMIT% --verbose > %LOG_FILE% 2>&1
set TEST_EXIT_CODE=%ERRORLEVEL%
echo Test completed with exit code: %TEST_EXIT_CODE%
if %TEST_EXIT_CODE% EQU 0 (
    echo ✅ Full system backup test PASSED!
) else (
    echo ❌ Full system backup test FAILED!
)
echo Log file: %LOG_FILE%
pause
exit /b %TEST_EXIT_CODE%

:test_performance
echo Running performance benchmark test...
set LOG_FILE=logs\tests\test_run_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log
%PYTHON_CMD% %TEST_SCRIPT% --performance-test --verbose > %LOG_FILE% 2>&1
set TEST_EXIT_CODE=%ERRORLEVEL%
echo Test completed with exit code: %TEST_EXIT_CODE%
if %TEST_EXIT_CODE% EQU 0 (
    echo ✅ Performance benchmark test PASSED!
) else (
    echo ❌ Performance benchmark test FAILED!
)
echo Log file: %LOG_FILE%
pause
exit /b %TEST_EXIT_CODE%

:test_error_handling
echo Running error handling test...
set LOG_FILE=logs\tests\test_run_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log
%PYTHON_CMD% %TEST_SCRIPT% --error-test --verbose > %LOG_FILE% 2>&1
set TEST_EXIT_CODE=%ERRORLEVEL%
echo Test completed with exit code: %TEST_EXIT_CODE%
if %TEST_EXIT_CODE% EQU 0 (
    echo ✅ Error handling test PASSED!
) else (
    echo ❌ Error handling test FAILED!
)
echo Log file: %LOG_FILE%
pause
exit /b %TEST_EXIT_CODE%

:test_all_tests
echo Running comprehensive test suite...
echo WARNING: This may take 15-30 minutes to complete.
set /p CONFIRM="Do you want to continue? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo Test cancelled by user.
    exit /b 0
)
set LOG_FILE=logs\tests\test_run_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log
%PYTHON_CMD% %TEST_SCRIPT% --all-tests --verbose > %LOG_FILE% 2>&1
set TEST_EXIT_CODE=%ERRORLEVEL%
echo Test completed with exit code: %TEST_EXIT_CODE%
if %TEST_EXIT_CODE% EQU 0 (
    echo ✅ ALL TESTS PASSED!
) else (
    echo ⚠️  SOME TESTS FAILED!
)
echo Log file: %LOG_FILE%
pause
exit /b %TEST_EXIT_CODE%

:test_quick
echo Running quick test suite...
set LOG_FILE=logs\tests\test_run_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log
%PYTHON_CMD% test_cleaned_system.py --quick > %LOG_FILE% 2>&1
set TEST_EXIT_CODE=%ERRORLEVEL%
echo Test completed with exit code: %TEST_EXIT_CODE%
if %TEST_EXIT_CODE% EQU 0 (
    echo ✅ Quick tests PASSED!
) else (
    echo ❌ Quick tests FAILED!
)
echo Log file: %LOG_FILE%
pause
exit /b %TEST_EXIT_CODE%

:test_help
echo ===============================================================
echo TNGD Backup System Testing Framework - Help
echo ===============================================================
echo.
echo Usage:
echo   run_daily_backup.bat test [test-type] [options]
echo.
echo Test Types:
echo   dry-run           Validate tables only, no backup
echo   single-table      Test backup with one table
echo   full-test         Test complete backup process
echo   performance       Run performance benchmarks
echo   error-handling    Test error scenarios
echo   all-tests         Run comprehensive test suite
echo   quick             Run quick test suite
echo   help              Show this help message
echo.
echo Examples:
echo   run_daily_backup.bat test dry-run
echo   run_daily_backup.bat test single-table
echo   run_daily_backup.bat test full-test 3
echo   run_daily_backup.bat test all-tests
echo.
pause
exit /b 0

:setup_mode
echo ===============================================================
echo TNGD DAILY BACKUP SCHEDULER SETUP
echo ===============================================================
echo.

REM Check setup command
set SETUP_CMD=%1
if "%SETUP_CMD%"=="" set SETUP_CMD=help

if "%SETUP_CMD%"=="help" goto setup_help
if "%SETUP_CMD%"=="create-daily" goto setup_create_daily
if "%SETUP_CMD%"=="create-weekly" goto setup_create_weekly
if "%SETUP_CMD%"=="create-monthly" goto setup_create_monthly
if "%SETUP_CMD%"=="create-all" goto setup_create_all
if "%SETUP_CMD%"=="list" goto setup_list
if "%SETUP_CMD%"=="status" goto setup_status
if "%SETUP_CMD%"=="delete" goto setup_delete

echo Unknown setup command: %SETUP_CMD%
goto setup_help

:setup_create_daily
echo Creating daily backup task...
set BACKUP_TIME=01:00
if "%2"=="--time" if not "%3"=="" set BACKUP_TIME=%3
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: This script must be run as Administrator to create scheduled tasks.
    pause
    exit /b 1
)
%PYTHON_CMD% %SCHEDULER_SCRIPT% --create --time %BACKUP_TIME% --task-name "TNGD_DailyBackup"
if %ERRORLEVEL% EQU 0 (
    echo ✅ Daily backup task created successfully!
) else (
    echo ❌ Failed to create daily backup task.
)
pause
exit /b %ERRORLEVEL%

:setup_create_weekly
echo Creating weekly backup task...
set BACKUP_TIME=02:00
set DAY_OF_WEEK=SUN
if "%2"=="--time" if not "%3"=="" set BACKUP_TIME=%3
if "%2"=="--day" if not "%3"=="" set DAY_OF_WEEK=%3
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: This script must be run as Administrator.
    pause
    exit /b 1
)
schtasks /create /tn "TNGD_WeeklyBackup" /tr "%~dp0run_daily_backup.bat --weekly" /sc weekly /d %DAY_OF_WEEK% /st %BACKUP_TIME% /ru SYSTEM /rl HIGHEST /f
if %ERRORLEVEL% EQU 0 (
    echo ✅ Weekly backup task created successfully!
) else (
    echo ❌ Failed to create weekly backup task.
)
pause
exit /b %ERRORLEVEL%

:setup_create_monthly
echo Creating monthly backup task...
set BACKUP_TIME=03:00
set DAY_OF_MONTH=1
if "%2"=="--time" if not "%3"=="" set BACKUP_TIME=%3
if "%2"=="--day" if not "%3"=="" set DAY_OF_MONTH=%3
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: This script must be run as Administrator.
    pause
    exit /b 1
)
schtasks /create /tn "TNGD_MonthlyBackup" /tr "%~dp0run_monthly_backup.bat" /sc monthly /d %DAY_OF_MONTH% /st %BACKUP_TIME% /ru SYSTEM /rl HIGHEST /f
if %ERRORLEVEL% EQU 0 (
    echo ✅ Monthly backup task created successfully!
) else (
    echo ❌ Failed to create monthly backup task.
)
pause
exit /b %ERRORLEVEL%

:setup_create_all
echo Creating all backup tasks...
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: This script must be run as Administrator.
    pause
    exit /b 1
)
%PYTHON_CMD% %SCHEDULER_SCRIPT% --create-all
if %ERRORLEVEL% EQU 0 (
    echo ✅ All backup tasks created successfully!
) else (
    echo ❌ Some tasks failed to create.
)
pause
exit /b %ERRORLEVEL%

:setup_list
echo Listing TNGD backup tasks...
%PYTHON_CMD% %SCHEDULER_SCRIPT% --list
pause
exit /b 0

:setup_status
echo Checking backup task status...
set TASK_NAME=TNGD_DailyBackup
if not "%2"=="" set TASK_NAME=%2
%PYTHON_CMD% %SCHEDULER_SCRIPT% --status --task-name %TASK_NAME%
pause
exit /b 0

:setup_delete
echo Deleting backup task...
set TASK_NAME=TNGD_DailyBackup
if not "%2"=="" set TASK_NAME=%2
echo WARNING: This will delete the backup task: %TASK_NAME%
set /p CONFIRM="Are you sure? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: This script must be run as Administrator.
    pause
    exit /b 1
)
%PYTHON_CMD% %SCHEDULER_SCRIPT% --delete --task-name %TASK_NAME%
if %ERRORLEVEL% EQU 0 (
    echo ✅ Task %TASK_NAME% deleted successfully!
) else (
    echo ❌ Failed to delete task %TASK_NAME%.
)
pause
exit /b %ERRORLEVEL%

:setup_help
echo ===============================================================
echo TNGD Daily Backup Scheduler Setup - Help
echo ===============================================================
echo.
echo Usage:
echo   run_daily_backup.bat setup [command] [options]
echo.
echo Commands:
echo   create-daily      Create daily backup task
echo   create-weekly     Create weekly backup task
echo   create-monthly    Create monthly backup task
echo   create-all        Create all backup tasks
echo   list              List all TNGD backup tasks
echo   status [task]     Show status of backup task
echo   delete [task]     Delete backup task
echo   help              Show this help message
echo.
echo Examples:
echo   run_daily_backup.bat setup create-daily --time 02:30
echo   run_daily_backup.bat setup create-all
echo   run_daily_backup.bat setup list
echo   run_daily_backup.bat setup status TNGD_DailyBackup
echo.
pause
exit /b 0

:help
echo ===============================================================
echo TNGD Daily Backup System - Unified Help
echo ===============================================================
echo.
echo This is the unified daily backup system that consolidates all
echo backup-related functionality into a single, easy-to-use interface.
echo.
echo Usage:
echo   run_daily_backup.bat [mode] [options]
echo.
echo MODES:
echo ===============================================================
echo.
echo 1. BACKUP MODE (default)
echo   run_daily_backup.bat [backup] [options]
echo
echo   Options:
echo     --dry-run          Validate tables only, no backup
echo     --single-table     Process only one table for testing
echo     --force-email      Send email even in test mode
echo     --verbose          Enable verbose logging
echo     --chunk-size N     Override default chunk size
echo     --timeout N        Override default timeout
echo.
echo   Examples:
echo     run_daily_backup.bat
echo     run_daily_backup.bat backup --dry-run
echo     run_daily_backup.bat --single-table --verbose
echo     run_daily_backup.bat --chunk-size 50000 --timeout 1800
echo.
echo 2. SINGLE TABLE MODE
echo   run_daily_backup.bat single-table ^<table_name^> [options]
echo
echo   Options:
echo     --list-tables      List all available tables
echo     --dry-run          Test mode - validate only, no backup
echo     --days N           Number of days to backup (default: 1)
echo     --chunk-size N     Chunk size for processing
echo     --verbose          Enable detailed logging
echo.
echo   Examples:
echo     run_daily_backup.bat single-table my.app.tngd.waf
echo     run_daily_backup.bat single-table --list-tables
echo     run_daily_backup.bat single-table my.app.tngd.waf --days 7
echo.
echo 3. TEST MODE
echo   run_daily_backup.bat test [test-type] [options]
echo
echo   Test Types:
echo     dry-run            Validate tables only
echo     single-table       Test single table backup
echo     full-test          Test complete backup process
echo     performance        Run performance benchmarks
echo     error-handling     Test error scenarios
echo     all-tests          Run comprehensive test suite
echo     quick              Run quick test suite
echo.
echo   Examples:
echo     run_daily_backup.bat test dry-run
echo     run_daily_backup.bat test single-table
echo     run_daily_backup.bat test all-tests
echo.
echo 4. SETUP MODE
echo   run_daily_backup.bat setup [command] [options]
echo
echo   Commands:
echo     create-daily       Create daily backup task
echo     create-weekly      Create weekly backup task
echo     create-monthly     Create monthly backup task
echo     create-all         Create all backup tasks
echo     list               List all TNGD backup tasks
echo     status [task]      Show status of backup task
echo     delete [task]      Delete backup task
echo.
echo   Examples:
echo     run_daily_backup.bat setup create-daily --time 02:30
echo     run_daily_backup.bat setup create-all
echo     run_daily_backup.bat setup list
echo.
echo ===============================================================
echo FEATURES:
echo ===============================================================
echo   ✅ Sequential table processing (one at a time)
echo   ✅ Comprehensive error handling with exponential backoff retry
echo   ✅ Email notifications with detailed summaries
echo   ✅ Automatic cleanup and validation
echo   ✅ Performance monitoring and reporting
echo   ✅ Chunked data extraction for large tables
echo   ✅ ZIP compression with organized OSS storage
echo   ✅ Immediate cleanup of temporary files
echo   ✅ Integrated testing framework
echo   ✅ Task scheduler setup and management
echo   ✅ Single table backup utility
echo.
echo ===============================================================
echo QUICK START:
echo ===============================================================
echo 1. Test the system:        run_daily_backup.bat test dry-run
echo 2. Run single table test:  run_daily_backup.bat test single-table
echo 3. Run daily backup:       run_daily_backup.bat
echo 4. Setup automation:       run_daily_backup.bat setup create-all
echo.
echo The daily backup system is designed for maximum reliability
echo and 100%% success rate. It processes tables one by one to
echo minimize resource usage and ensure each backup completes
echo successfully before starting the next.
echo.
echo ===============================================================
pause
exit /b 0
