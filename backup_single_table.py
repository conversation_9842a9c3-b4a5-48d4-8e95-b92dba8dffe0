#!/usr/bin/env python3
"""
Single Table Backup Script for TNGD

This script allows you to backup a specific table with full control over the process.
It provides detailed logging and progress reporting for single table operations.

Usage:
    python backup_single_table.py <table_name> [options]
    python backup_single_table.py my.app.tngd.waf
    python backup_single_table.py my.app.tngd.waf --days 7 --chunk-size 50000
    python backup_single_table.py my.app.tngd.waf --dry-run

Examples:
    # Backup WAF table for last 1 day
    python backup_single_table.py my.app.tngd.waf

    # Backup with custom settings
    python backup_single_table.py my.app.tngd.waf --days 3 --chunk-size 100000

    # Test without actual backup
    python backup_single_table.py my.app.tngd.waf --dry-run

    # List available tables
    python backup_single_table.py --list-tables
"""

import os
import sys
import argparse
import logging
import datetime
import json
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.backup_config import BackupConfig
from core.config_manager import ConfigManager
from core.unified_table_processor import UnifiedTableProcessor
from utils.logging_utils import init_enhanced_logging, log_section_header
from utils.logging_utils import UnifiedLogger as LogManager
from utils.disk_cleanup import cleanup_temp_files

# Configure logging
logger = logging.getLogger(__name__)

class SingleTableBackup:
    """Single table backup utility with detailed control and monitoring."""

    def __init__(self, config_manager: ConfigManager = None):
        """
        Initialize single table backup utility.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.start_time = datetime.datetime.now()

    def list_available_tables(self) -> list:
        """
        List all available tables from configuration.

        Returns:
            List of available table names
        """
        try:
            tables = self.config_manager.get_tables_from_file()
            return tables or []
        except Exception as e:
            logger.error(f"Error loading table list: {str(e)}")
            return []

    def validate_table_name(self, table_name: str) -> bool:
        """
        Validate that the table name exists in configuration.

        Args:
            table_name: Name of the table to validate

        Returns:
            True if table exists, False otherwise
        """
        available_tables = self.list_available_tables()
        return table_name in available_tables

    def create_backup_config(self, args) -> BackupConfig:
        """
        Create backup configuration for single table backup.

        Args:
            args: Command line arguments

        Returns:
            BackupConfig object
        """
        config = BackupConfig()

        # Set configuration from arguments
        config.days = args.days
        config.chunk_size = args.chunk_size
        config.max_retries = args.max_retries
        config.retry_delay = args.retry_delay
        config.timeout = args.timeout
        config.max_concurrent_tables = 1  # Single table
        config.validate = args.validate

        return config

    def backup_table(self, table_name: str, args) -> dict:
        """
        Backup a single table.

        Args:
            table_name: Name of the table to backup
            args: Command line arguments

        Returns:
            Dictionary with backup results
        """
        log_section_header(f"SINGLE TABLE BACKUP: {table_name}")
        logger.info(f"Starting backup for table: {table_name}")
        logger.info(f"Days: {args.days}, Chunk size: {args.chunk_size}")

        # Validate table name
        if not self.validate_table_name(table_name):
            available_tables = self.list_available_tables()
            logger.error(f"Table '{table_name}' not found in configuration")
            logger.info(f"Available tables: {len(available_tables)}")
            for i, table in enumerate(available_tables[:10], 1):
                logger.info(f"  {i}. {table}")
            if len(available_tables) > 10:
                logger.info(f"  ... and {len(available_tables) - 10} more tables")
            return {
                'status': 'error',
                'error': f"Table '{table_name}' not found in configuration",
                'available_tables': available_tables
            }

        # Create backup configuration
        config = self.create_backup_config(args)

        try:
            if args.dry_run:
                return self._run_dry_run(table_name, config)
            else:
                return self._run_actual_backup(table_name, config)

        except Exception as e:
            logger.error(f"Backup failed for table {table_name}: {str(e)}")
            return {
                'status': 'error',
                'table_name': table_name,
                'error': str(e)
            }

    def _run_dry_run(self, table_name: str, config: BackupConfig) -> dict:
        """
        Run dry-run validation for the table.

        Args:
            table_name: Name of the table
            config: Backup configuration

        Returns:
            Validation results
        """
        logger.info(f"Running dry-run validation for table: {table_name}")

        # Use unified table processor for validation
        processor = UnifiedTableProcessor(config, strategy='smart')

        # This would validate the table without backing up
        logger.info(f"Validating table accessibility and data...")
        
        # For now, we'll simulate validation
        # In a real implementation, this would check table access and row count
        logger.info(f"Table {table_name} is accessible")
        logger.info(f"Estimated rows: [would be calculated]")
        logger.info(f"Estimated backup size: [would be calculated]")

        return {
            'status': 'success',
            'mode': 'dry_run',
            'table_name': table_name,
            'validation': {
                'accessible': True,
                'estimated_rows': 'N/A (dry run)',
                'estimated_size': 'N/A (dry run)'
            }
        }

    def _run_actual_backup(self, table_name: str, config: BackupConfig) -> dict:
        """
        Run actual backup for the table.

        Args:
            table_name: Name of the table
            config: Backup configuration

        Returns:
            Backup results
        """
        logger.info(f"Starting actual backup for table: {table_name}")

        # Use unified table processor for backup
        processor = UnifiedTableProcessor(config, strategy='smart')

        # Process single table
        result = processor.process_tables([table_name], skip_empty=False)

        # Extract results for this specific table
        summary = result.get('summary', {})
        table_results = result.get('table_results', {}).get(table_name, {})

        return {
            'status': 'success' if table_results.get('status') == 'success' else 'failed',
            'mode': 'actual_backup',
            'table_name': table_name,
            'result': table_results,
            'summary': summary
        }

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Single Table Backup for TNGD',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python backup_single_table.py my.app.tngd.waf
  python backup_single_table.py my.app.tngd.waf --days 7 --chunk-size 50000
  python backup_single_table.py my.app.tngd.waf --dry-run
  python backup_single_table.py --list-tables
        """
    )

    parser.add_argument('table_name', nargs='?',
                       help='Name of the table to backup')
    parser.add_argument('--list-tables', action='store_true',
                       help='List all available tables')
    parser.add_argument('--dry-run', action='store_true',
                       help='Validate table only, do not backup')
    parser.add_argument('--days', type=int, default=1,
                       help='Number of days to backup (default: 1)')
    parser.add_argument('--chunk-size', type=int, default=100000,
                       help='Chunk size for data extraction (default: 100000)')
    parser.add_argument('--max-retries', type=int, default=5,
                       help='Maximum number of retries (default: 5)')
    parser.add_argument('--retry-delay', type=int, default=5,
                       help='Delay between retries in seconds (default: 5)')
    parser.add_argument('--timeout', type=int, default=1800,
                       help='Timeout in seconds (default: 1800)')
    parser.add_argument('--validate', action='store_true', default=True,
                       help='Validate backup after completion (default: True)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')

    return parser.parse_args()

def main():
    """Main function for single table backup."""
    args = parse_arguments()

    # Initialize logging
    log_manager = LogManager()
    with log_manager.get_daily_logger_context(mode='single_table_backup') as (daily_logger, log_file_path):
        global logger
        logger = daily_logger

        # Initialize enhanced logging
        if args.verbose:
            init_enhanced_logging(console_level=logging.DEBUG)
        else:
            init_enhanced_logging(console_level=logging.INFO)

        # Create backup utility
        backup_util = SingleTableBackup()

        # Handle list tables request
        if args.list_tables:
            log_section_header("AVAILABLE TABLES")
            tables = backup_util.list_available_tables()
            logger.info(f"Found {len(tables)} tables in configuration:")
            for i, table in enumerate(tables, 1):
                logger.info(f"  {i:2d}. {table}")
            return 0

        # Validate arguments
        if not args.table_name:
            logger.error("Table name is required. Use --list-tables to see available tables.")
            return 1

        # Log startup
        log_section_header("SINGLE TABLE BACKUP UTILITY")
        logger.info(f"Log file: {log_file_path}")
        logger.info(f"Started at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # Run backup
            result = backup_util.backup_table(args.table_name, args)

            # Log results
            if result['status'] == 'success':
                logger.info("Single table backup completed successfully")
                if not args.dry_run:
                    cleanup_temp_files(force=True)
                return 0
            else:
                logger.error(f"Single table backup failed: {result.get('error', 'Unknown error')}")
                return 1

        except KeyboardInterrupt:
            logger.warning("Single table backup interrupted by user")
            return 1
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return 1

if __name__ == "__main__":
    sys.exit(main())
