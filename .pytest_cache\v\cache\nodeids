["tests/test_config_manager.py::TestConfigManager::test_get_default_value", "tests/test_config_manager.py::TestConfigManager::test_get_env", "tests/test_config_manager.py::TestConfigManager::test_get_env_default", "tests/test_config_manager.py::TestConfigManager::test_get_with_type_validation_bool", "tests/test_config_manager.py::TestConfigManager::test_get_with_type_validation_error", "tests/test_config_manager.py::TestConfigManager::test_get_with_type_validation_int", "tests/test_config_manager.py::TestConfigManager::test_load_config_file", "tests/test_config_manager.py::TestConfigManager::test_load_config_file_not_found", "tests/test_security_fixes.py::TestCredentialExposurePrevention::test_devo_credentials_not_stored_as_instance_variables", "tests/test_security_fixes.py::TestCredentialExposurePrevention::test_oss_credentials_not_stored_as_instance_variables", "tests/test_security_fixes.py::TestCredentialExposurePrevention::test_smtp_password_not_logged", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_secure_query_builder_validates_parameters", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_table_name_sanitization_allows_valid_names", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_table_name_sanitization_blocks_injection", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_where_clause_validation_blocks_injection", "tests/test_security_fixes.py::TestSecurityValidation::test_no_hardcoded_credentials_in_code"]