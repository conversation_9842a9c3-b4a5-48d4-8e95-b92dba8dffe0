["tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_common_imports_available", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_moved_test_file_imports_correctly", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_no_duplicate_test_files_in_core", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_project_path_manager_context", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_project_path_setup", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_standard_backup_config_creation", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_standard_config_creation", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_storage_manager_test_removed", "tests/test_code_duplication_fixes.py::TestCodeDuplicationFixes::test_unified_table_processor_test_moved", "tests/test_code_duplication_fixes.py::TestCodeOrganization::test_common_utilities_consolidation", "tests/test_code_duplication_fixes.py::TestCodeOrganization::test_no_production_code_in_tests", "tests/test_code_duplication_fixes.py::TestCodeOrganization::test_tests_directory_structure", "tests/test_config_manager.py::TestConfigManager::test_get_default_value", "tests/test_config_manager.py::TestConfigManager::test_get_env", "tests/test_config_manager.py::TestConfigManager::test_get_env_default", "tests/test_config_manager.py::TestConfigManager::test_get_with_type_validation_bool", "tests/test_config_manager.py::TestConfigManager::test_get_with_type_validation_error", "tests/test_config_manager.py::TestConfigManager::test_get_with_type_validation_int", "tests/test_config_manager.py::TestConfigManager::test_load_config_file", "tests/test_config_manager.py::TestConfigManager::test_load_config_file_not_found", "tests/test_security_fixes.py::TestCredentialExposurePrevention::test_devo_credentials_not_stored_as_instance_variables", "tests/test_security_fixes.py::TestCredentialExposurePrevention::test_oss_credentials_not_stored_as_instance_variables", "tests/test_security_fixes.py::TestCredentialExposurePrevention::test_smtp_password_not_logged", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_secure_query_builder_validates_parameters", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_table_name_sanitization_allows_valid_names", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_table_name_sanitization_blocks_injection", "tests/test_security_fixes.py::TestSQLInjectionPrevention::test_where_clause_validation_blocks_injection", "tests/test_security_fixes.py::TestSecurityValidation::test_no_hardcoded_credentials_in_code", "tests/test_security_fixes.py::TestTemporaryFileSecurity::test_storage_manager_uses_secure_temp_files", "tests/test_security_fixes.py::TestTemporaryFileSecurity::test_table_processor_uses_secure_temp_directories", "tests/test_security_fixes.py::TestTemporaryFileSecurity::test_verification_uses_secure_temp_files"]