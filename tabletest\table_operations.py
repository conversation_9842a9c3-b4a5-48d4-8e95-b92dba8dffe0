#!/usr/bin/env python3
"""
Table Operations Module

This module provides functions for testing and managing Devo tables.
It includes functionality to check table existence, get metadata, validate tables, and manage the table list.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Tuple

# Import project modules
from core.devo_client import DevoClient
from core.config_manager import ConfigManager
from utils.error_handler import (
    ApiError,
    NetworkError,
    handle_error,
    safe_execute
)

# Configure logging
logger = logging.getLogger(__name__)

def check_table_exists(table_name: str) -> bool:
    """
    Check if a table exists in Devo.

    Args:
        table_name: Name of the table to check

    Returns:
        True if the table exists, False otherwise
    """
    try:
        client = DevoClient()
        return client.check_table_exists(table_name)
    except Exception as e:
        logger.error(f"Error checking if table {table_name} exists: {str(e)}")
        return False

def get_table_metadata(table_name: str) -> Optional[Dict[str, Any]]:
    """
    Get metadata for a table.

    Args:
        table_name: Name of the table

    Returns:
        Dictionary with table metadata or None if an error occurs
    """
    try:
        client = DevoClient()
        
        # Check if the table exists
        if not client.check_table_exists(table_name):
            logger.warning(f"Table {table_name} does not exist")
            return None
        
        # Get the columns
        columns = client.get_table_columns(table_name)
        
        # Get the row count (this might be an estimate)
        try:
            row_count = client.get_table_count(table_name)
        except Exception as e:
            logger.warning(f"Could not get row count for table {table_name}: {str(e)}")
            row_count = None
        
        # Calculate size estimate if we have row count and columns
        size_estimate = None
        if row_count is not None and columns:
            # Rough estimate: assume 100 bytes per column per row
            size_bytes = row_count * len(columns) * 100
            
            # Convert to human-readable format
            if size_bytes < 1024:
                size_estimate = f"{size_bytes} bytes"
            elif size_bytes < 1024 * 1024:
                size_estimate = f"{size_bytes / 1024:.2f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                size_estimate = f"{size_bytes / (1024 * 1024):.2f} MB"
            else:
                size_estimate = f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
        
        # Return the metadata
        return {
            'table_name': table_name,
            'columns': columns,
            'row_count': row_count,
            'size_estimate': size_estimate
        }
    except Exception as e:
        logger.error(f"Error getting metadata for table {table_name}: {str(e)}")
        return None

def get_table_sample(table_name: str, sample_size: int = 5) -> Optional[List[Dict[str, Any]]]:
    """
    Get sample data from a table.

    Args:
        table_name: Name of the table
        sample_size: Number of rows to retrieve (default: 5)

    Returns:
        List of sample rows or None if an error occurs
    """
    try:
        client = DevoClient()
        
        # Check if the table exists
        if not client.check_table_exists(table_name):
            logger.warning(f"Table {table_name} does not exist")
            return None
        
        # Query the table for sample data
        query = f"from {table_name} select * limit {sample_size}"
        results = client.execute_query(query, days=1, timeout=30)
        
        return results
    except Exception as e:
        logger.error(f"Error getting sample data for table {table_name}: {str(e)}")
        return None

def validate_table_for_backup(table_name: str) -> Dict[str, Any]:
    """
    Validate a table for backup.

    Args:
        table_name: Name of the table to validate

    Returns:
        Dictionary with validation results
    """
    result = {
        'table_name': table_name,
        'valid': False,
        'accessible': False,
        'has_data': False,
        'columns': [],
        'errors': []
    }
    
    try:
        client = DevoClient()
        
        # Check if the table exists
        if not client.check_table_exists(table_name):
            result['errors'].append(f"Table {table_name} does not exist or is not accessible")
            return result
        
        # Table exists
        result['accessible'] = True
        
        # Get the columns
        columns = client.get_table_columns(table_name)
        
        if not columns:
            result['errors'].append(f"Table {table_name} has no columns or could not retrieve column information")
            return result
        
        result['columns'] = columns
        
        # Check if the table has data
        try:
            # Try to get a single row
            query = f"from {table_name} select * limit 1"
            sample = client.execute_query(query, days=1, timeout=30)
            
            if sample:
                result['has_data'] = True
            else:
                # No data is not necessarily an error, but we'll note it
                logger.info(f"Table {table_name} exists but has no data")
        except Exception as e:
            result['errors'].append(f"Could not check if table {table_name} has data: {str(e)}")
            return result
        
        # If we got here, the table is valid for backup
        result['valid'] = True
        
        return result
    except Exception as e:
        error_msg = f"Error validating table {table_name}: {str(e)}"
        logger.error(error_msg)
        result['errors'].append(error_msg)
        return result

def add_table_to_list(table_name: str) -> Dict[str, Any]:
    """
    Add a table to the backup list.

    Args:
        table_name: Name of the table to add

    Returns:
        Dictionary with the result of the operation
    """
    result = {
        'success': False,
        'error': None
    }
    
    try:
        # Get the current table list
        config = ConfigManager()
        tables_file = 'tabletest/tables.json'
        
        # Load the current tables
        try:
            with open(tables_file, 'r') as f:
                tables = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # Create a new list if the file doesn't exist or is invalid
            tables = []
        
        # Check if the table is already in the list
        if table_name in tables:
            result['error'] = f"Table {table_name} is already in the backup list"
            return result
        
        # Add the table to the list
        tables.append(table_name)
        
        # Sort the list alphabetically
        tables.sort()
        
        # Save the updated list
        with open(tables_file, 'w') as f:
            json.dump(tables, f, indent=2)
        
        result['success'] = True
        logger.info(f"Added table {table_name} to backup list")
        
        return result
    except Exception as e:
        error_msg = f"Error adding table {table_name} to backup list: {str(e)}"
        logger.error(error_msg)
        result['error'] = error_msg
        return result

def remove_table_from_list(table_name: str) -> Dict[str, Any]:
    """
    Remove a table from the backup list.

    Args:
        table_name: Name of the table to remove

    Returns:
        Dictionary with the result of the operation
    """
    result = {
        'success': False,
        'error': None
    }
    
    try:
        # Get the current table list
        config = ConfigManager()
        tables_file = 'tabletest/tables.json'
        
        # Load the current tables
        try:
            with open(tables_file, 'r') as f:
                tables = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            result['error'] = "Could not read the backup list"
            return result
        
        # Check if the table is in the list
        if table_name not in tables:
            result['error'] = f"Table {table_name} is not in the backup list"
            return result
        
        # Remove the table from the list
        tables.remove(table_name)
        
        # Save the updated list
        with open(tables_file, 'w') as f:
            json.dump(tables, f, indent=2)
        
        result['success'] = True
        logger.info(f"Removed table {table_name} from backup list")
        
        return result
    except Exception as e:
        error_msg = f"Error removing table {table_name} from backup list: {str(e)}"
        logger.error(error_msg)
        result['error'] = error_msg
        return result

def get_all_tables() -> List[str]:
    """
    Get all tables from the backup list.

    Returns:
        List of table names
    """
    try:
        config = ConfigManager()
        return config.get_tables_from_file()
    except Exception as e:
        logger.error(f"Error getting tables from backup list: {str(e)}")
        return []
